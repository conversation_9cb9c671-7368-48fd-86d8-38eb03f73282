<?php
require_once 'config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档目录提取器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Word文档目录提取器</h1>
            <p>上传Word文档，自动提取其中的标题目录</p>
        </header>

        <main>
            <div class="upload-section">
                <form action="upload.php" method="post" enctype="multipart/form-data" class="upload-form">
                    <div class="file-input-wrapper">
                        <input type="file" name="wordFile" id="wordFile" accept=".doc,.docx" required>
                        <label for="wordFile" class="file-input-label">
                            <span class="file-icon">📄</span>
                            <span class="file-text">选择Word文档</span>
                        </label>
                    </div>
                    
                    <div class="upload-info">
                        <p>支持的格式：.doc, .docx</p>
                        <p>最大文件大小：50MB</p>
                    </div>
                    
                    <button type="submit" class="upload-btn">
                        <span class="btn-icon">🚀</span>
                        提取目录
                    </button>
                </form>
            </div>

            <div class="features">
                <h2>功能特点</h2>
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon">📋</div>
                        <h3>自动识别标题</h3>
                        <p>智能识别文档中的各级标题</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <h3>层级结构</h3>
                        <p>保持原文档的标题层级关系</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <h3>快速处理</h3>
                        <p>高效解析，快速生成目录</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🔒</div>
                        <h3>安全可靠</h3>
                        <p>文件处理后自动清理</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Word文档目录提取器. 使用PHP开发.</p>
        </footer>
    </div>

    <script>
        // 文件选择交互
        document.getElementById('wordFile').addEventListener('change', function(e) {
            const label = document.querySelector('.file-input-label .file-text');
            if (e.target.files.length > 0) {
                label.textContent = e.target.files[0].name;
            } else {
                label.textContent = '选择Word文档';
            }
        });

        // 拖拽上传
        const uploadForm = document.querySelector('.upload-form');
        const fileInput = document.getElementById('wordFile');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadForm.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadForm.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadForm.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadForm.classList.add('drag-over');
        }

        function unhighlight(e) {
            uploadForm.classList.remove('drag-over');
        }

        uploadForm.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                fileInput.files = files;
                const label = document.querySelector('.file-input-label .file-text');
                label.textContent = files[0].name;
            }
        }
    </script>
</body>
</html>
