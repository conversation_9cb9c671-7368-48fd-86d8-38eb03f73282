<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处理结果 - Word文档目录提取器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Word文档目录提取器</h1>
            <nav>
                <a href="index.php" class="back-btn">← 返回首页</a>
            </nav>
        </header>

        <main>
            <div class="result-section">
                <?php if (isset($error)): ?>
                    <div class="error-message">
                        <div class="error-icon">❌</div>
                        <h2>处理失败</h2>
                        <p><?php echo htmlspecialchars($error); ?></p>
                        <a href="index.php" class="retry-btn">重新上传</a>
                    </div>
                <?php elseif (isset($success) && $success): ?>
                    <div class="success-message">
                        <div class="success-icon">✅</div>
                        <h2><?php echo htmlspecialchars($message); ?></h2>
                        <p>文件：<strong><?php echo htmlspecialchars($originalFileName); ?></strong></p>
                    </div>

                    <div class="toc-result">
                        <?php echo $tocHtml; ?>
                    </div>

                    <div class="actions">
                        <button onclick="printToc()" class="action-btn print-btn">
                            <span class="btn-icon">🖨️</span>
                            打印目录
                        </button>
                        <button onclick="copyToc()" class="action-btn copy-btn">
                            <span class="btn-icon">📋</span>
                            复制目录
                        </button>
                        <a href="index.php" class="action-btn upload-btn">
                            <span class="btn-icon">📄</span>
                            上传新文档
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Word文档目录提取器. 使用PHP开发.</p>
        </footer>
    </div>

    <script>
        function printToc() {
            const tocContent = document.querySelector('.toc-result').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>文档目录</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .toc { max-width: 800px; }
                        .toc h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                        .toc-list { list-style: none; padding: 0; }
                        .toc-list li { margin: 8px 0; padding: 8px; border-left: 3px solid #007bff; }
                        .toc-level-1 { margin-left: 0; font-weight: bold; font-size: 1.1em; }
                        .toc-level-2 { margin-left: 20px; }
                        .toc-level-3 { margin-left: 40px; }
                        .toc-level-4 { margin-left: 60px; }
                        .toc-text { display: inline-block; width: 80%; }
                        .toc-level { float: right; background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }
                    </style>
                </head>
                <body>${tocContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function copyToc() {
            const tocList = document.querySelectorAll('.toc-list li');
            let textContent = '文档目录\\n\\n';
            
            tocList.forEach(item => {
                const text = item.querySelector('.toc-text').textContent;
                const level = item.querySelector('.toc-level').textContent;
                const indent = '  '.repeat(parseInt(level.replace('H', '')) - 1);
                textContent += `${indent}${text}\\n`;
            });

            navigator.clipboard.writeText(textContent).then(() => {
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<span class="btn-icon">✅</span>已复制';
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }).catch(err => {
                alert('复制失败，请手动选择文本复制');
            });
        }
    </script>
</body>
</html>
