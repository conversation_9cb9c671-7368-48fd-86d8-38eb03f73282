<?php
require_once 'config.php';

use App\WordProcessor;

// 检查是否有文件上传
if (!isset($_FILES['wordFile']) || $_FILES['wordFile']['error'] !== UPLOAD_ERR_OK) {
    $error = '请选择一个有效的Word文档文件';
    include 'result.php';
    exit;
}

$uploadedFile = $_FILES['wordFile'];
$fileName = $uploadedFile['name'];
$fileTmpName = $uploadedFile['tmp_name'];
$fileSize = $uploadedFile['size'];

// 验证文件大小
if ($fileSize > MAX_FILE_SIZE) {
    $error = '文件大小超过限制（最大50MB）';
    include 'result.php';
    exit;
}

// 验证文件扩展名
$fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
    $error = '不支持的文件格式，请上传.doc或.docx文件';
    include 'result.php';
    exit;
}

// 生成唯一文件名
$uniqueFileName = uniqid() . '_' . $fileName;
$uploadPath = UPLOAD_DIR . $uniqueFileName;

// 移动上传的文件
if (!move_uploaded_file($fileTmpName, $uploadPath)) {
    $error = '文件上传失败，请重试';
    include 'result.php';
    exit;
}

try {
    // 处理Word文档
    $processor = new WordProcessor($uploadPath);
    $processor->loadDocument();
    
    // 提取目录
    $toc = $processor->extractTableOfContents();
    
    // 格式化为HTML
    $tocHtml = $processor->formatTocAsHtml($toc);
    
    // 成功信息
    $success = true;
    $message = '目录提取成功！';
    $originalFileName = $fileName;
    
} catch (Exception $e) {
    $error = '处理文档时出错：' . $e->getMessage();
    $tocHtml = '';
} finally {
    // 清理上传的文件
    if (file_exists($uploadPath)) {
        unlink($uploadPath);
    }
}

// 包含结果页面
include 'result.php';
?>
