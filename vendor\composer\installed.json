{"packages": [{"name": "phpoffice/math", "version": "0.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "f0f8cad98624459c540cdd61d2a174d834471773"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/f0f8cad98624459c540cdd61d2a174d834471773", "reference": "f0f8cad98624459c540cdd61d2a174d834471773", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "time": "2023-09-25T12:08:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/", "Tests\\PhpOffice\\Math\\": "tests/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "support": {"issues": "https://github.com/PHPOffice/Math/issues", "source": "https://github.com/PHPOffice/Math/tree/0.1.0"}, "install-path": "../phpoffice/math"}, {"name": "phpoffice/phpword", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "e76b701ef538cb749641514fcbc31a68078550fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/e76b701ef538cb749641514fcbc31a68078550fa", "reference": "e76b701ef538cb749641514fcbc31a68078550fa", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.1"}, "require-dev": {"dompdf/dompdf": "^2.0", "ext-gd": "*", "ext-libxml": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpstan/phpstan-phpunit": "@stable", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "time": "2023-11-30T11:22:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "support": {"issues": "https://github.com/PHPOffice/PHPWord/issues", "source": "https://github.com/PHPOffice/PHPWord/tree/1.2.0"}, "install-path": "../phpoffice/phpword"}], "dev": true, "dev-package-names": []}