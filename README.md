# Word文档目录提取器

一个基于PHP开发的Web应用程序，用于上传Word文档并自动提取其中的标题目录。

## 功能特点

- 📄 支持.doc和.docx格式的Word文档
- 🎯 智能识别文档中的真正目录标题（非所有粗体文本）
- 📋 保持原文档的标题层级关系
- ⚡ 快速处理，实时生成目录
- 🔒 安全可靠，文件处理后自动清理
- 📱 响应式设计，支持移动设备
- 🧠 智能过滤，只提取章节标题和目录项

## 系统要求

- PHP 7.4 或更高版本
- Composer（用于依赖管理）
- Web服务器（Apache、Nginx或PHP内置服务器）

## 安装步骤

1. **克隆或下载项目文件**
   ```bash
   # 如果使用Git
   git clone <repository-url>
   cd word-toc-extractor
   ```

2. **安装PHP依赖**
   ```bash
   composer install
   ```

3. **启动Web服务器**
   
   使用PHP内置服务器（开发环境）：
   ```bash
   php -S localhost:8000
   ```
   
   或配置Apache/Nginx指向项目目录

4. **访问应用**
   
   在浏览器中打开：`http://localhost:8000`

## 使用方法

1. 打开网站首页
2. 点击"选择Word文档"按钮或拖拽文件到上传区域
3. 选择.doc或.docx格式的Word文档（最大50MB）
4. 点击"提取目录"按钮
5. 查看提取的目录结构
6. 可以打印或复制目录内容

## 项目结构

```
word-toc-extractor/
├── index.php              # 主页面
├── upload.php             # 文件上传处理
├── result.php             # 结果显示页面
├── config.php             # 配置文件
├── style.css              # 样式文件
├── src/
│   └── WordProcessor.php  # Word文档处理类
├── uploads/               # 临时上传目录（自动创建）
├── vendor/                # Composer依赖
├── composer.json          # Composer配置
└── README.md              # 说明文档
```

## 技术栈

- **后端**: PHP 7.4+
- **Word文档处理**: PHPOffice/PhpWord
- **前端**: HTML5, CSS3, JavaScript
- **依赖管理**: Composer

## 安全特性

- 文件类型验证（仅允许.doc和.docx）
- 文件大小限制（最大50MB）
- 上传文件自动清理
- 错误处理和异常捕获

## 开发和测试

### 测试Word文档处理功能

```bash
php test_word_processing.php
```

### 自定义配置

编辑`config.php`文件可以修改：
- 上传文件大小限制
- 允许的文件扩展名
- 上传目录路径

## 故障排除

### 常见问题

1. **"Class 'App\WordProcessor' not found"错误**
   ```bash
   composer dump-autoload
   ```

2. **文件上传失败**
   - 检查uploads目录权限
   - 确认PHP配置允许文件上传
   - 检查文件大小是否超过限制

3. **Word文档解析失败**
   - 确认文件格式正确（.doc或.docx）
   - 检查文件是否损坏
   - 查看错误日志获取详细信息

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.1.0 (2024-06-06)
- 优化目录提取算法，更精确地识别真正的标题
- 智能过滤非标题内容（如页码、编号、说明文字等）
- 支持标准的章节编号格式识别
- 改进标题层级判断逻辑

### v1.0.0 (2024-06-06)
- 初始版本发布
- 支持Word文档目录提取
- 响应式Web界面
- 基本的安全特性
