{"name": "phpoffice/phpword", "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "keywords": ["PHP", "PHPOffice", "office", "PHPWord", "word", "template", "template processor", "reader", "writer", "docx", "OOXML", "OpenXML", "Office Open XML", "ISO IEC 29500", "WordprocessingML", "RTF", "Rich Text Format", "doc", "odt", "ODF", "OpenDocument", "PDF", "HTML"], "homepage": "https://phpoffice.github.io/PHPWord/", "type": "library", "license": "LGPL-3.0", "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "scripts": {"test": ["phpunit --color=always"], "test-no-coverage": ["phpunit --color=always --no-coverage"], "check": ["php-cs-fixer fix --ansi --dry-run --diff", "phpcs --report-width=200 --report-summary --report-full samples/ src/ tests/ --ignore=src/PhpWord/Shared/PCLZip --standard=PSR2 -n", "phpmd src/,tests/ text ./phpmd.xml.dist --exclude pclzip.lib.php", "@test-no-coverage", "phpstan analyse --ansi"], "fix": ["php-cs-fixer fix --ansi"]}, "scripts-descriptions": {"test": "Runs all unit tests", "test-no-coverage": "Runs all unit tests, without code coverage", "check": "Runs PHP CheckStyle and PHP Mess detector", "fix": "Fixes issues found by PHP-CS"}, "config": {"platform": {"php": "8.0"}}, "require": {"php": "^7.1|^8.0", "ext-dom": "*", "ext-json": "*", "ext-xml": "*", "phpoffice/math": "^0.1"}, "require-dev": {"ext-zip": "*", "ext-gd": "*", "ext-libxml": "*", "dompdf/dompdf": "^2.0", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpunit/phpunit": ">=7.0", "tecnickcom/tcpdf": "^6.5", "symfony/process": "^4.4 || ^5.0", "friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan-phpunit": "@stable"}, "suggest": {"ext-zip": "Allows writing OOXML and ODF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "dompdf/dompdf": "Allows writing PDF"}, "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "autoload-dev": {"psr-4": {"PhpOffice\\PhpWordTests\\": "tests/PhpWordTests"}}}