/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2em;
    opacity: 0.9;
}

nav {
    margin-top: 20px;
}

.back-btn {
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    background: rgba(255,255,255,0.2);
    border-radius: 25px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

/* 主要内容 */
main {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* 上传区域 */
.upload-section {
    text-align: center;
    margin-bottom: 60px;
}

.upload-form {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 40px;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.upload-form.drag-over {
    border-color: #007bff;
    background: #e3f2fd;
}

.file-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

#wordFile {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: inline-block;
    padding: 20px 40px;
    background: #007bff;
    color: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1em;
}

.file-input-label:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.file-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

.upload-info {
    margin: 20px 0;
    color: #6c757d;
}

.upload-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40,167,69,0.3);
}

.btn-icon {
    margin-right: 8px;
}

/* 功能特点 */
.features {
    margin-top: 40px;
}

.features h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-item {
    text-align: center;
    padding: 30px 20px;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.feature-item h3 {
    margin-bottom: 10px;
    color: #333;
}

/* 结果页面样式 */
.result-section {
    text-align: center;
}

.error-message, .success-message {
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.error-icon, .success-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.retry-btn {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 25px;
    background: #dc3545;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* 目录样式 */
.toc-result {
    text-align: left;
    margin: 30px 0;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
}

.toc {
    max-width: 100%;
}

.toc h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.toc-list {
    list-style: none;
    padding: 0;
}

.toc-list li {
    margin: 10px 0;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.toc-list li:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toc-level-1 {
    margin-left: 0;
    font-weight: bold;
    font-size: 1.1em;
    border-left-color: #dc3545;
}

.toc-level-2 {
    margin-left: 20px;
    border-left-color: #fd7e14;
}

.toc-level-3 {
    margin-left: 40px;
    border-left-color: #ffc107;
}

.toc-level-4 {
    margin-left: 60px;
    border-left-color: #28a745;
}

.toc-text {
    flex: 1;
}

.toc-level {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

/* 操作按钮 */
.actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1em;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.print-btn {
    background: #6f42c1;
    color: white;
}

.print-btn:hover {
    background: #5a32a3;
}

.copy-btn {
    background: #17a2b8;
    color: white;
}

.copy-btn:hover {
    background: #138496;
}

.upload-btn {
    background: #28a745;
    color: white;
}

.upload-btn:hover {
    background: #218838;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 底部 */
footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    main {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 200px;
        justify-content: center;
    }
    
    .toc-level-2,
    .toc-level-3,
    .toc-level-4 {
        margin-left: 10px;
    }
}
