<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'mycompany/word-toc-extractor',
        'dev' => true,
    ),
    'versions' => array(
        'mycompany/word-toc-extractor' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'phpoffice/math' => array(
            'pretty_version' => '0.1.0',
            'version' => '0.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/math',
            'aliases' => array(),
            'reference' => 'f0f8cad98624459c540cdd61d2a174d834471773',
            'dev_requirement' => false,
        ),
        'phpoffice/phpword' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpword',
            'aliases' => array(),
            'reference' => 'e76b701ef538cb749641514fcbc31a68078550fa',
            'dev_requirement' => false,
        ),
    ),
);
