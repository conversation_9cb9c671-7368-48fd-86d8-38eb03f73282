<?php

namespace App;

use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Element\AbstractElement;

/**
 * Word文档处理类
 */
class WordProcessor
{
    private $filePath;
    private $phpWordObject;
    
    public function __construct($filePath)
    {
        $this->filePath = $filePath;
    }
    
    /**
     * 加载Word文档
     */
    public function loadDocument()
    {
        try {
            $this->phpWordObject = IOFactory::load($this->filePath);
            return true;
        } catch (\Exception $e) {
            throw new \Exception("无法加载Word文档: " . $e->getMessage());
        }
    }
    
    /**
     * 提取目录标题
     */
    public function extractTableOfContents()
    {
        if (!$this->phpWordObject) {
            throw new \Exception("请先加载文档");
        }

        $toc = [];
        $sections = $this->phpWordObject->getSections();

        foreach ($sections as $section) {
            $elements = $section->getElements();
            $this->processElements($elements, $toc);
        }

        // 过滤和清理目录项
        $toc = $this->filterAndCleanToc($toc);

        return $toc;
    }
    
    /**
     * 递归处理文档元素
     */
    private function processElements($elements, &$toc)
    {
        foreach ($elements as $element) {
            if (method_exists($element, 'getElements')) {
                $this->processElements($element->getElements(), $toc);
            }
            
            // 检查是否为标题
            if ($this->isHeading($element)) {
                $headingInfo = $this->extractHeadingInfo($element);
                if ($headingInfo) {
                    $toc[] = $headingInfo;
                }
            }
        }
    }
    
    /**
     * 判断是否为标题元素
     */
    private function isHeading($element)
    {
        // 首先检查是否有标题样式
        if (method_exists($element, 'getStyle')) {
            $style = $element->getStyle();
            if ($style) {
                // 检查段落样式名称
                if (method_exists($style, 'getStyleName')) {
                    $styleName = $style->getStyleName();
                    if ($styleName && preg_match('/^(heading|标题|title|Heading)/i', $styleName)) {
                        return true;
                    }
                }

                // 检查大纲级别
                if (method_exists($style, 'getOutlineLevel')) {
                    $outlineLevel = $style->getOutlineLevel();
                    if ($outlineLevel !== null && $outlineLevel >= 1 && $outlineLevel <= 9) {
                        return true;
                    }
                }
            }
        }

        // 检查段落样式
        if (get_class($element) === 'PhpOffice\PhpWord\Element\TextRun') {
            $paragraphStyle = $element->getParagraphStyle();
            if ($paragraphStyle) {
                if (method_exists($paragraphStyle, 'getStyleName')) {
                    $styleName = $paragraphStyle->getStyleName();
                    if ($styleName && preg_match('/^(heading|标题|title|Heading)/i', $styleName)) {
                        return true;
                    }
                }

                if (method_exists($paragraphStyle, 'getOutlineLevel')) {
                    $outlineLevel = $paragraphStyle->getOutlineLevel();
                    if ($outlineLevel !== null && $outlineLevel >= 1 && $outlineLevel <= 9) {
                        return true;
                    }
                }
            }

            return $this->checkTextRunForHeading($element);
        }

        if (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
            return $this->checkTextForHeading($element);
        }

        return false;
    }
    
    /**
     * 检查TextRun是否为标题
     */
    private function checkTextRunForHeading($textRun)
    {
        $text = '';
        $isBold = false;
        $fontSize = 12;
        $hasNumbering = false;

        $elements = $textRun->getElements();
        foreach ($elements as $element) {
            if (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
                $text .= $element->getText();
                $style = $element->getFontStyle();
                if ($style) {
                    if ($style->isBold()) {
                        $isBold = true;
                    }
                    $fontSize = max($fontSize, $style->getSize() ?: 12);
                }
            }
        }

        // 检查是否符合目录标题的特征
        return $this->isLikelyTocHeading($text, $isBold, $fontSize);
    }
    
    /**
     * 检查Text是否为标题
     */
    private function checkTextForHeading($textElement)
    {
        $text = $textElement->getText();
        $style = $textElement->getFontStyle();
        $isBold = $style && $style->isBold();
        $fontSize = $style ? ($style->getSize() ?: 12) : 12;

        return $this->isLikelyTocHeading($text, $isBold, $fontSize);
    }
    
    /**
     * 提取标题信息
     */
    private function extractHeadingInfo($element)
    {
        $text = '';
        $level = 1;
        $fontSize = 12;
        $outlineLevel = null;

        // 首先尝试从样式中获取大纲级别
        if (method_exists($element, 'getStyle')) {
            $style = $element->getStyle();
            if ($style && method_exists($style, 'getOutlineLevel')) {
                $outlineLevel = $style->getOutlineLevel();
            }
        }

        // 检查段落样式
        if (get_class($element) === 'PhpOffice\PhpWord\Element\TextRun') {
            $paragraphStyle = $element->getParagraphStyle();
            if ($paragraphStyle && method_exists($paragraphStyle, 'getOutlineLevel')) {
                $outlineLevel = $paragraphStyle->getOutlineLevel();
            }

            $elements = $element->getElements();
            foreach ($elements as $subElement) {
                if (get_class($subElement) === 'PhpOffice\PhpWord\Element\Text') {
                    $text .= $subElement->getText();
                    $style = $subElement->getFontStyle();
                    if ($style) {
                        $fontSize = max($fontSize, $style->getSize() ?: 12);
                    }
                }
            }
        } elseif (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
            $text = $element->getText();
            $style = $element->getFontStyle();
            if ($style) {
                $fontSize = $style->getSize() ?: 12;
            }
        }

        // 确定标题级别
        if ($outlineLevel !== null && $outlineLevel >= 1 && $outlineLevel <= 9) {
            $level = $outlineLevel;
        } else {
            // 根据字体大小确定标题级别
            if ($fontSize >= 22) {
                $level = 1;
            } elseif ($fontSize >= 18) {
                $level = 2;
            } elseif ($fontSize >= 16) {
                $level = 3;
            } elseif ($fontSize >= 14) {
                $level = 4;
            } else {
                $level = 5;
            }
        }

        $text = trim($text);
        if (empty($text)) {
            return null;
        }

        return [
            'text' => $text,
            'level' => $level,
            'fontSize' => $fontSize,
            'outlineLevel' => $outlineLevel
        ];
    }
    
    /**
     * 格式化目录为HTML
     */
    public function formatTocAsHtml($toc)
    {
        if (empty($toc)) {
            return '<p>未找到标题内容</p>';
        }
        
        $html = '<div class="toc">';
        $html .= '<h3>文档目录</h3>';
        $html .= '<ul class="toc-list">';
        
        foreach ($toc as $item) {
            $class = 'toc-level-' . $item['level'];
            $html .= '<li class="' . $class . '">';
            $html .= '<span class="toc-text">' . htmlspecialchars($item['text']) . '</span>';
            $html .= '<span class="toc-level">H' . $item['level'] . '</span>';
            $html .= '</li>';
        }
        
        $html .= '</ul>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * 判断文本是否像目录标题
     */
    private function isLikelyTocHeading($text, $isBold, $fontSize)
    {
        $text = trim($text);

        // 排除空文本或过短的文本
        if (empty($text) || mb_strlen($text) < 3) {
            return false;
        }

        // 排除纯数字、纯符号、纯英文
        if (preg_match('/^[\d\s\-\.\(\)（）a-zA-Z]+$/', $text)) {
            return false;
        }

        // 排除常见的非标题内容
        $excludePatterns = [
            '/^(项目编号|报告编号|备案证明编号|系统编号)[:：]?/',
            '/^\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?$/',
            '/^第[一二三四五六七八九十\d]+[段组页条项位]/',
            '/^[（\(]\d+[）\)]/',
            '/^说明[:：]?$/',
            '/未(基于|配置|采用|开启|设置|重命名|删除|制定|提供|定期|组织|预先)/',
            '/^[A-Z]\.\d+/',  // 如 A.1, B.2 等附录编号
            '/^附录[A-Z]/',   // 附录标识
            '/^\d+\.\d+\.\d+/',  // 版本号格式
            '/^第\d+页/',
            '/^共\d+页/',
            '/^[\d\s\-\.\(\)（）]+$/',
            '/本次测评不包含/',
            '/本次测评不涉及/',
            '/在.*方面采取了以下安全措施/',
            '/在.*方面存在如下安全问题/',
            '/在.*方面，未发现明显的安全问题/',
        ];

        foreach ($excludePatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }

        // 必须满足以下条件之一才可能是标题
        $isHeadingLike = false;

        // 1. 标准的目录编号格式（最重要的判断条件）
        // 匹配 "1 测评项目概述", "1.1 测评目的", "3.4.1 网络设备" 等格式
        if (preg_match('/^\d+(\.\d+)*\s+[\x{4e00}-\x{9fa5}]/u', $text)) {
            $isHeadingLike = true;
        }

        // 2. 附录格式
        if (preg_match('/^附录[A-Z]\s+/', $text)) {
            $isHeadingLike = true;
        }

        // 3. 特定的标题格式（如 "C.1 安全物理环境"）
        if (preg_match('/^[A-Z]\.\d+(\.\d+)*\s+[\x{4e00}-\x{9fa5}]/u', $text)) {
            $isHeadingLike = true;
        }

        // 4. 特殊的标题（如 "渗透测试目标", "渗透测试方法概述" 等）
        $specialTitles = [
            '渗透测试目标', '渗透测试方法概述', '渗透测试原则', '渗透测试工具',
            '云平台等级测评结论', '等级测评结论扩展表'
        ];

        foreach ($specialTitles as $title) {
            if ($text === $title) {
                $isHeadingLike = true;
                break;
            }
        }

        // 5. 文本长度检查（标题通常不会太长）
        if (mb_strlen($text) > 50) {
            $isHeadingLike = false;
        }

        // 6. 排除明显的内容文本
        if (mb_strlen($text) > 30 && !preg_match('/^\d+(\.\d+)*\s+/', $text)) {
            $isHeadingLike = false;
        }

        return $isHeadingLike;
    }

    /**
     * 过滤和清理目录项
     */
    private function filterAndCleanToc($toc)
    {
        $filtered = [];
        $seenTexts = [];

        foreach ($toc as $item) {
            $text = trim($item['text']);

            // 去重
            if (in_array($text, $seenTexts)) {
                continue;
            }

            // 进一步过滤
            if ($this->shouldIncludeInToc($text)) {
                $seenTexts[] = $text;
                $filtered[] = $item;
            }
        }

        return $filtered;
    }

    /**
     * 判断是否应该包含在目录中
     */
    private function shouldIncludeInToc($text)
    {
        // 最小长度检查
        if (mb_strlen($text) < 3) {
            return false;
        }

        // 排除明显不是标题的内容
        $excludePatterns = [
            '/^[\d\s\-\.\(\)（）]+$/',  // 纯数字和符号
            '/^[a-zA-Z\d\s\-\.\(\)（）]+$/',  // 纯英文数字符号
            '/^\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/',  // 日期
            '/^第\d+页/',  // 页码
            '/^共\d+页/',  // 页码
            '/^[\d\s]+$/',  // 纯数字
        ];

        foreach ($excludePatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }

        return true;
    }
}
