<?php

namespace App;

use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Element\AbstractElement;

/**
 * Word文档处理类
 */
class WordProcessor
{
    private $filePath;
    private $phpWordObject;
    
    public function __construct($filePath)
    {
        $this->filePath = $filePath;
    }
    
    /**
     * 加载Word文档
     */
    public function loadDocument()
    {
        try {
            $this->phpWordObject = IOFactory::load($this->filePath);
            return true;
        } catch (\Exception $e) {
            throw new \Exception("无法加载Word文档: " . $e->getMessage());
        }
    }
    
    /**
     * 提取目录标题
     */
    public function extractTableOfContents()
    {
        if (!$this->phpWordObject) {
            throw new \Exception("请先加载文档");
        }
        
        $toc = [];
        $sections = $this->phpWordObject->getSections();
        
        foreach ($sections as $section) {
            $elements = $section->getElements();
            $this->processElements($elements, $toc);
        }
        
        return $toc;
    }
    
    /**
     * 递归处理文档元素
     */
    private function processElements($elements, &$toc)
    {
        foreach ($elements as $element) {
            if (method_exists($element, 'getElements')) {
                $this->processElements($element->getElements(), $toc);
            }
            
            // 检查是否为标题
            if ($this->isHeading($element)) {
                $headingInfo = $this->extractHeadingInfo($element);
                if ($headingInfo) {
                    $toc[] = $headingInfo;
                }
            }
        }
    }
    
    /**
     * 判断是否为标题元素
     */
    private function isHeading($element)
    {
        if (get_class($element) === 'PhpOffice\PhpWord\Element\TextRun') {
            return $this->checkTextRunForHeading($element);
        }
        
        if (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
            return $this->checkTextForHeading($element);
        }
        
        return false;
    }
    
    /**
     * 检查TextRun是否为标题
     */
    private function checkTextRunForHeading($textRun)
    {
        $elements = $textRun->getElements();
        foreach ($elements as $element) {
            if (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
                $style = $element->getFontStyle();
                if ($style && ($style->isBold() || $style->getSize() > 12)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 检查Text是否为标题
     */
    private function checkTextForHeading($text)
    {
        $style = $text->getFontStyle();
        return $style && ($style->isBold() || $style->getSize() > 12);
    }
    
    /**
     * 提取标题信息
     */
    private function extractHeadingInfo($element)
    {
        $text = '';
        $level = 1;
        $fontSize = 12;
        
        if (get_class($element) === 'PhpOffice\PhpWord\Element\TextRun') {
            $elements = $element->getElements();
            foreach ($elements as $subElement) {
                if (get_class($subElement) === 'PhpOffice\PhpWord\Element\Text') {
                    $text .= $subElement->getText();
                    $style = $subElement->getFontStyle();
                    if ($style) {
                        $fontSize = max($fontSize, $style->getSize() ?: 12);
                    }
                }
            }
        } elseif (get_class($element) === 'PhpOffice\PhpWord\Element\Text') {
            $text = $element->getText();
            $style = $element->getFontStyle();
            if ($style) {
                $fontSize = $style->getSize() ?: 12;
            }
        }
        
        // 根据字体大小确定标题级别
        if ($fontSize >= 18) {
            $level = 1;
        } elseif ($fontSize >= 16) {
            $level = 2;
        } elseif ($fontSize >= 14) {
            $level = 3;
        } else {
            $level = 4;
        }
        
        $text = trim($text);
        if (empty($text)) {
            return null;
        }
        
        return [
            'text' => $text,
            'level' => $level,
            'fontSize' => $fontSize
        ];
    }
    
    /**
     * 格式化目录为HTML
     */
    public function formatTocAsHtml($toc)
    {
        if (empty($toc)) {
            return '<p>未找到标题内容</p>';
        }
        
        $html = '<div class="toc">';
        $html .= '<h3>文档目录</h3>';
        $html .= '<ul class="toc-list">';
        
        foreach ($toc as $item) {
            $class = 'toc-level-' . $item['level'];
            $html .= '<li class="' . $class . '">';
            $html .= '<span class="toc-text">' . htmlspecialchars($item['text']) . '</span>';
            $html .= '<span class="toc-level">H' . $item['level'] . '</span>';
            $html .= '</li>';
        }
        
        $html .= '</ul>';
        $html .= '</div>';
        
        return $html;
    }
}
